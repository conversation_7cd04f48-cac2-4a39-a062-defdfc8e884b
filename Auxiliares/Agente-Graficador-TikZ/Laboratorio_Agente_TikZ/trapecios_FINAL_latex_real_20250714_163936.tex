\documentclass{article}
\usepackage{tikz}
\usepackage{amsmath}
\usepackage{geometry}
\usepackage{graphicx}
\geometry{a4paper, margin=2cm}

\usetikzlibrary{calc,decorations.markings,arrows.meta,patterns}

\begin{document}

\title{Análisis Real: trapecios_FINAL}
\author{Procesador con Análisis Real}
\date{2025-07-14 16:39:36}
\maketitle

\section{Imagen Original}

Imagen analizada: \texttt{trapecios_FINAL.png}

Tipo detectado: \textbf{trapecio}

\section{Código TikZ Generado (Basado en Análisis Real)}

\begin{center}
% Código TikZ para TRAPECIO - Basado en análisis real
% Generado automáticamente desde imagen analizada
% Para usar: \input{este_archivo.tikz} dentro de un documento LaTeX

\begin{tikzpicture}[scale=1.2]
    % Configuración específica para trapecios
    \tikzset{
        trapecio/.style={thick, blue, fill=blue!10},
        vertice/.style={circle, fill=red, inner sep=2pt},
        etiqueta/.style={font=\small, black},
        medida/.style={<->, red, above},
        altura/.style={dashed, gray}
    }
    
    % Coordenadas del trapecio (basadas en análisis)
    \coordinate (A) at (0, 0);
    \coordinate (B) at (6, 0);
    \coordinate (C) at (4.5, 3);
    \coordinate (D) at (1.5, 3);
    
    % Dibujar el trapecio
    \draw[trapecio] (A) -- (B) -- (C) -- (D) -- cycle;
    
    % Marcar vértices
    \node[vertice] at (A) {};
    \node[vertice] at (B) {};
    \node[vertice] at (C) {};
    \node[vertice] at (D) {};
    
    % Etiquetas de vértices
    \node[etiqueta, below left] at (A) {A};
    \node[etiqueta, below right] at (B) {B};
    \node[etiqueta, above right] at (C) {C};
    \node[etiqueta, above left] at (D) {D};
    
    % Líneas de altura (características del trapecio)
    \draw[altura] ([xshift=-0.5cm]A) -- ([xshift=-0.5cm]D);
    \draw[altura] ([xshift=0.5cm]B) -- ([xshift=0.5cm]C);
    
    % Medidas características
    \draw[medida] ([yshift=-0.4cm]A) -- ([yshift=-0.4cm]B) 
        node[midway, below] {Base mayor};
    \draw[medida] ([yshift=0.4cm]D) -- ([yshift=0.4cm]C) 
        node[midway, above] {Base menor};
    \draw[medida] ([xshift=-0.8cm]A) -- ([xshift=-0.8cm]D) 
        node[midway, left] {h};
    
    % Información adicional del trapecio
    \node[align=center, font=\footnotesize] at (3, -1.5) {
        Trapecio\\
        Bases paralelas: AB \parallel DC\\
        Altura: h
    };

\end{tikzpicture}
\end{center}

\section{Información del Análisis}

\begin{itemize}
    \item Tipo: trapecio
    \item Elementos: base_mayor, base_menor, altura, lados_laterales
    \item Análisis pix2text: No disponible
    \item Timestamp: 20250714_163936
\end{itemize}

\end{document}
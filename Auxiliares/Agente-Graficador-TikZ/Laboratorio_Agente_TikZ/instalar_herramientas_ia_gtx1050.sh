#!/bin/bash
# Script de instalación optimizado para GTX 1050 - Manjaro Plasma
# Creado después de instantánea TimeShift: 2025-07-14_12-11-05

echo "🚀 INSTALANDO HERRAMIENTAS IA PARA GTX 1050..."
echo "📸 Instantánea TimeShift creada: 2025-07-14_12-11-05"
echo ""

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Verificar que estamos en Manjaro
if ! command -v pacman &> /dev/null; then
    error "Este script está diseñado para Manjaro/Arch Linux"
    exit 1
fi

# Verificar GPU NVIDIA
if ! nvidia-smi &> /dev/null; then
    warning "NVIDIA GPU no detectada o drivers no instalados"
    echo "Continuando con instalación CPU..."
fi

log "Paso 1: Verificando entorno virtual..."
VENV_PATH="$HOME/ai_math_tools_gtx1050"

if [ ! -d "$VENV_PATH" ]; then
    log "Creando entorno virtual..."
    python -m venv "$VENV_PATH"
fi

log "Paso 2: Activando entorno virtual..."
source "$VENV_PATH/bin/activate"

log "Paso 3: Actualizando pip..."
pip install --upgrade pip

log "Paso 4: Instalando dependencias básicas..."
pip install --no-cache-dir numpy pillow requests

log "Paso 5: Instalando Pix2Text (herramienta principal)..."
pip install --no-cache-dir pix2text[multilingual]

log "Paso 6: Instalando PyTorch optimizado para GTX 1050..."
# Usar CPU por defecto para evitar problemas de VRAM
pip install --no-cache-dir torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

log "Paso 7: Instalando Transformers para Florence-2..."
pip install --no-cache-dir transformers accelerate

log "Paso 8: Instalando DeTikZify versión ligera..."
pip install --no-cache-dir git+https://github.com/potamides/DeTikZify.git

log "Paso 9: Creando script de activación..."
cat > "$HOME/activar_ia_matematicas.sh" << 'EOF'
#!/bin/bash
# Script para activar herramientas IA matemáticas

echo "🧮 Activando herramientas IA para matemáticas..."
source ~/ai_math_tools_gtx1050/bin/activate

echo "✅ Entorno activado!"
echo ""
echo "📚 Herramientas disponibles:"
echo "  • pix2text - OCR matemático superior"
echo "  • python -c 'import torch; print(torch.cuda.is_available())' - Verificar CUDA"
echo "  • detikzify - Generación TikZ (usar con precaución en GTX 1050)"
echo ""
echo "💡 Uso recomendado:"
echo "  1. Para fórmulas: pix2text"
echo "  2. Para análisis general: Florence-2"
echo "  3. Para TikZ simple: DeTikZify con CPU"
echo ""
echo "🔧 Para desactivar: deactivate"

# Configurar para usar CPU por defecto (evitar problemas VRAM)
export CUDA_VISIBLE_DEVICES=""
echo "⚠️  Configurado para usar CPU (GTX 1050 tiene solo 2GB VRAM)"
echo "   Para forzar GPU: unset CUDA_VISIBLE_DEVICES"

bash
EOF

chmod +x "$HOME/activar_ia_matematicas.sh"

log "Paso 10: Creando script de prueba..."
cat > "$HOME/probar_herramientas_ia.py" << 'EOF'
#!/usr/bin/env python3
"""
Script de prueba para herramientas IA matemáticas
Optimizado para GTX 1050 (2GB VRAM)
"""

import sys
import torch
import numpy as np
from PIL import Image

def test_basic_setup():
    """Prueba configuración básica"""
    print("🔍 Probando configuración básica...")
    
    print(f"✅ Python: {sys.version}")
    print(f"✅ NumPy: {np.__version__}")
    print(f"✅ PyTorch: {torch.__version__}")
    
    # Verificar CUDA
    if torch.cuda.is_available():
        print(f"✅ CUDA disponible: {torch.cuda.get_device_name(0)}")
        print(f"📊 VRAM total: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        print(f"📊 VRAM libre: {torch.cuda.memory_reserved(0) / 1024**3:.1f}GB")
    else:
        print("⚠️  CUDA no disponible, usando CPU")

def test_pix2text():
    """Prueba Pix2Text"""
    print("\n🔍 Probando Pix2Text...")
    try:
        from pix2text import Pix2Text
        p2t = Pix2Text()
        print("✅ Pix2Text cargado correctamente")
        return True
    except Exception as e:
        print(f"❌ Error en Pix2Text: {e}")
        return False

def test_transformers():
    """Prueba Transformers"""
    print("\n🔍 Probando Transformers...")
    try:
        from transformers import pipeline
        print("✅ Transformers disponible")
        return True
    except Exception as e:
        print(f"❌ Error en Transformers: {e}")
        return False

def test_detikzify():
    """Prueba DeTikZify"""
    print("\n🔍 Probando DeTikZify...")
    try:
        import detikzify
        print("✅ DeTikZify disponible")
        print("⚠️  Recomendado usar con CPU para GTX 1050")
        return True
    except Exception as e:
        print(f"❌ Error en DeTikZify: {e}")
        return False

def main():
    print("🧮 PRUEBA DE HERRAMIENTAS IA MATEMÁTICAS")
    print("=" * 50)
    
    test_basic_setup()
    
    tests = [
        test_pix2text,
        test_transformers,
        test_detikzify
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 RESULTADO: {passed}/{len(tests)} herramientas funcionando")
    
    if passed == len(tests):
        print("🎉 ¡Todas las herramientas instaladas correctamente!")
        print("\n💡 Próximos pasos:")
        print("  1. Ejecutar: ~/activar_ia_matematicas.sh")
        print("  2. Probar con una imagen matemática")
        print("  3. Usar principalmente Pix2Text para tu GTX 1050")
    else:
        print("⚠️  Algunas herramientas necesitan configuración adicional")

if __name__ == "__main__":
    main()
EOF

chmod +x "$HOME/probar_herramientas_ia.py"

log "✅ INSTALACIÓN COMPLETADA!"
echo ""
echo -e "${BLUE}📋 RESUMEN DE INSTALACIÓN:${NC}"
echo "  📁 Entorno virtual: ~/ai_math_tools_gtx1050"
echo "  🚀 Activar: ~/activar_ia_matematicas.sh"
echo "  🧪 Probar: python ~/probar_herramientas_ia.py"
echo ""
echo -e "${YELLOW}⚠️  IMPORTANTE PARA GTX 1050:${NC}"
echo "  • Solo 2GB VRAM - usar principalmente CPU"
echo "  • Pix2Text es la herramienta principal recomendada"
echo "  • DeTikZify usar con precaución (puede agotar VRAM)"
echo ""
echo -e "${GREEN}🎯 HERRAMIENTAS INSTALADAS:${NC}"
echo "  ✅ Pix2Text - OCR matemático superior"
echo "  ✅ PyTorch - Framework base (CPU optimizado)"
echo "  ✅ Transformers - Para Florence-2"
echo "  ✅ DeTikZify - Generación TikZ (usar con CPU)"
echo ""
echo -e "${BLUE}📸 Punto de restauración disponible:${NC}"
echo "  sudo timeshift --restore --snapshot '2025-07-14_12-11-05'"
echo ""
echo "🚀 Para empezar: ./activar_ia_matematicas.sh"

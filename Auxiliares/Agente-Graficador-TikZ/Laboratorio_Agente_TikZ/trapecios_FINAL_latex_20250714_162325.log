This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2026/dev/Arch Linux) (preloaded format=pdflatex 2025.7.9)  14 JUL 2025 16:23
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**trapecios_FINAL_latex_20250714_162325.tex
(./trapecios_FINAL_latex_20250714_162325.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(/usr/share/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks17
\pgfutil@tempdima=\dimen142
\pgfutil@tempdimb=\dimen143
)
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box52
)
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
)
(/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(/usr/share/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen144
\Gin@req@width=\dimen145
)
(/usr/share/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks19
\pgfkeys@temptoks=\toks20

(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.te
x
\pgfkeys@tmptoks=\toks21
))
\pgf@x=\dimen146
\pgf@y=\dimen147
\pgf@xa=\dimen148
\pgf@ya=\dimen149
\pgf@xb=\dimen150
\pgf@yb=\dimen151
\pgf@xc=\dimen152
\pgf@yc=\dimen153
\pgf@xd=\dimen154
\pgf@yd=\dimen155
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count270
\c@pgf@countb=\count271
\c@pgf@countc=\count272
\c@pgf@countd=\count273
\t@pgf@toka=\toks22
\t@pgf@tokb=\toks23
\t@pgf@tokc=\toks24
\pgf@sys@id@count=\count274
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count275
\pgfsyssoftpath@bigbuffer@items=\count276
)
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen156
\pgfmath@count=\count277
\pgfmath@box=\box53
\pgfmath@toks=\toks25
\pgfmath@stack@operand=\toks26
\pgfmath@stack@operation=\toks27
)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code
.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.te
x) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics
.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count278
))
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen157
\pgf@picmaxx=\dimen158
\pgf@picminy=\dimen159
\pgf@picmaxy=\dimen160
\pgf@pathminx=\dimen161
\pgf@pathmaxx=\dimen162
\pgf@pathminy=\dimen163
\pgf@pathmaxy=\dimen164
\pgf@xx=\dimen165
\pgf@xy=\dimen166
\pgf@yx=\dimen167
\pgf@yy=\dimen168
\pgf@zx=\dimen169
\pgf@zy=\dimen170
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen171
\pgf@path@lasty=\dimen172
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen173
\pgf@shorten@start@additional=\dimen174
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box54
\pgf@hbox=\box55
\pgf@layerbox@main=\box56
\pgf@picture@serial@count=\count279
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen175
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.t
ex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen176
\pgf@pt@y=\dimen177
\pgf@pt@temp=\dimen178
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.te
x
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen179
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen180
\pgf@sys@shading@range@num=\count280
\pgf@shadingcount=\count281
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box57
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box58
)
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen181
\pgf@nodesepend=\dimen182
)
(/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen183
\pgffor@skip=\dimen184
\pgffor@stack=\toks28
\pgffor@toks=\toks29
))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.te
x
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count282
\pgfplotmarksize=\dimen185
)
\tikz@lastx=\dimen186
\tikz@lasty=\dimen187
\tikz@lastxsaved=\dimen188
\tikz@lastysaved=\dimen189
\tikz@lastmovetox=\dimen190
\tikz@lastmovetoy=\dimen191
\tikzleveldistance=\dimen192
\tikzsiblingdistance=\dimen193
\tikz@figbox=\box59
\tikz@figbox@bg=\box60
\tikz@tempbox=\box61
\tikz@tempbox@bg=\box62
\tikztreelevel=\count283
\tikznumberofchildren=\count284
\tikznumberofcurrentchild=\count285
\tikz@fig@count=\count286
 (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count287
\pgfmatrixcurrentcolumn=\count288
\pgf@matrix@numberofcolumns=\count289
)
\tikz@expandcount=\count290

(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
topaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/pgfplots/pgfplots.sty
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)

(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
\t@pgfplots@toka=\toks30
\t@pgfplots@tokb=\toks31
\t@pgfplots@tokc=\toks32
\pgfplots@tmpa=\dimen194
\c@pgfplots@coordindex=\count291
\c@pgfplots@scanlineindex=\count292

(/usr/share/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_l
oader.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks33
\t@pgf@tokb=\toks34
\t@pgf@tokc=\toks35

(/usr/share/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_p
gfutil-common-lists.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructure
.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructure
ext.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsarray.code.te
x
\c@pgfplotsarray@tmp=\count293
)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsmatrix.code.t
ex)
(/usr/share/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstableshared.code.t
ex
\c@pgfplotstable@counta=\count294
\t@pgfplotstable@a=\toks36
)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsdeque.code.te
x) (/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.data.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplots.surfshading
.code.tex
\c@pgfplotslibrarysurf@no=\count295

(/usr/share/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surfshading.
pgfsys-pdftex.def)))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandler.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.code.tex)))
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessing.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.code.tex
\pgfdecoratedcompleteddistance=\dimen195
\pgfdecoratedremainingdistance=\dimen196
\pgfdecoratedinputsegmentcompleteddistance=\dimen197
\pgfdecoratedinputsegmentremainingdistance=\dimen198
\pgf@decorate@distancetomove=\dimen199
\pgf@decorate@repeatstate=\count296
\pgfdecorationsegmentamplitude=\dimen256
\pgfdecorationsegmentlength=\dimen257
)
\tikz@lib@dec@box=\box63
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.pathmorphing.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.pathmorphing.code.tex))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.pathreplacing.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.pathreplacing.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplots.contourlua
.code.tex)
\pgfplots@numplots=\count297
\pgfplots@xmin@reg=\dimen258
\pgfplots@xmax@reg=\dimen259
\pgfplots@ymin@reg=\dimen260
\pgfplots@ymax@reg=\dimen261
\pgfplots@zmin@reg=\dimen262
\pgfplots@zmax@reg=\dimen263
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
plotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmarks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks37
\ex@=\dimen264
))
(/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen265
)
(/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count298
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count299
\leftroot@=\count300
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count301
\DOTSCASE@=\count302
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box64
\strutbox@=\box65
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen266
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count303
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count304
\dotsspace@=\muskip17
\c@parentequation=\count305
\dspbrk@lvl=\count306
\tag@help=\toks38
\row@=\count307
\column@=\count308
\maxfields@=\count309
\andhelp@=\toks39
\eqnshift@=\dimen267
\alignsep@=\dimen268
\tagshift@=\dimen269
\tagwidth@=\dimen270
\totwidth@=\dimen271
\lineht@=\dimen272
\@envbody=\toks40
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks41
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/share/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/share/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count310
\Gm@cntv=\count311
\c@Gm@tempcnt=\count312
\Gm@bindingoffset=\dimen273
\Gm@wd@mp=\dimen274
\Gm@odd@mp=\dimen275
\Gm@even@mp=\dimen276
\Gm@layoutwidth=\dimen277
\Gm@layoutheight=\dimen278
\Gm@layouthoffset=\dimen279
\Gm@layoutvoffset=\dimen280
\Gm@dimlist=\toks42
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
calc.code.tex
File: tikzlibrarycalc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.markings.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.markings.code.tex))
(/usr/share/texmf-dist/tex/latex/pgf-blur/tikzlibraryshadows.blur.code.tex
 v1.02, 2012/12/09
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
shadows.code.tex
File: tikzlibraryshadows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
fadings.code.tex
File: tikzlibraryfadings.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryfadings.code.tex
File: pgflibraryfadings.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
\pgfbs@fading@count=\count313
)
(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.meta.code.tex
File: pgflibraryarrows.meta.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowinset=\dimen281
\pgfarrowlength=\dimen282
\pgfarrowwidth=\dimen283
\pgfarrowlinewidth=\dimen284
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
patterns.code.tex
File: tikzlibrarypatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibrarypatterns.code.tex
File: pgflibrarypatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count314
\l__pdf_internal_box=\box66
)
No file trapecios_FINAL_latex_20250714_162325.aux.
\openout1 = `trapecios_FINAL_latex_20250714_162325.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
(/usr/share/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count315
\scratchdimen=\dimen285
\scratchbox=\box67
\nofMPsegments=\count316
\nofMParguments=\count317
\everyMPshowfont=\toks43
\MPscratchCnt=\count318
\MPscratchDim=\dimen286
\MPnumerator=\count319
\makeMPintoPDFobject=\count320
\everyMPtoPDFconversion=\toks44
) (/usr/share/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/share/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))

Package pgfplots Warning: running in backwards compatibility mode (unsuitable t
ick labels; missing features). Consider writing \pgfplotsset{compat=1.18} into 
your preamble.
 on input line 12.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(56.9055pt, 483.69687pt, 56.9055pt)
* v-part:(T,H,B)=(56.9055pt, 731.23584pt, 56.9055pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=483.69687pt
* \textheight=731.23584pt
* \oddsidemargin=-15.36449pt
* \evensidemargin=-15.36449pt
* \topmargin=-52.36449pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

! Missing $ inserted.
<inserted text> 
                $
l.17 \maketitle
               
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Missing $ inserted.
<inserted text> 
                $
l.17 \maketitle
               
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Missing } inserted.
<inserted text> 
                }
l.17 \maketitle
               
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

! Extra }, or forgotten \endgroup.
\@maketitle ...note \thanks {\LARGE \@title \par }
                                                  \vskip 1.5em{\large \lines...
l.17 \maketitle
               
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.

! Missing $ inserted.
<inserted text> 
                $
l.21 ...en procesada: \texttt{trapecios_FINAL.png}
                                                  
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Extra }, or forgotten $.
<recently read> \egroup 
                        
l.21 ...en procesada: \texttt{trapecios_FINAL.png}
                                                  
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.

! Missing $ inserted.
<inserted text> 
                $
l.22 
     
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Missing $ inserted.
<inserted text> 
                $
l.84 ...vo procesado: \texttt{trapecios_FINAL.png}
                                                  
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Extra }, or forgotten $.
<recently read> \egroup 
                        
l.84 ...vo procesado: \texttt{trapecios_FINAL.png}
                                                  
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.


! LaTeX Error: Command \item invalid in math mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.85     \item
               Timestamp: \texttt{20250714_162325}
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

! Missing $ inserted.
<inserted text> 
                $
l.85     \item T
                imestamp: \texttt{20250714_162325}
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Missing $ inserted.
<inserted text> 
                $
l.85     \item Timestamp: \texttt{20250714_162325}
                                                  
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Extra }, or forgotten $.
<recently read> \egroup 
                        
l.85     \item Timestamp: \texttt{20250714_162325}
                                                  
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.


! LaTeX Error: Command \item invalid in math mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.86     \item
               Sistema: Procesador automático completo v2.0
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

! Missing $ inserted.
<inserted text> 
                $
l.86     \item S
                istema: Procesador automático completo v2.0
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Missing $ inserted.
<inserted text> 
                $
l.87 ...e-Graficador-TikZ/Laboratorio_Agente_TikZ}
                                                  
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Double subscript.
<argument> ...Drive/RepositorioMatematicasICFES_R_
                                                  Exams/Auxiliares/Agente-Gr...
l.87 ...e-Graficador-TikZ/Laboratorio_Agente_TikZ}
                                                  
I treat `x_1_2' essentially like `x_1{}_2'.

! Extra }, or forgotten $.
<recently read> \egroup 
                        
l.87 ...e-Graficador-TikZ/Laboratorio_Agente_TikZ}
                                                  
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.


! LaTeX Error: Command \item invalid in math mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.88     \item
               Archivos generados:
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

! Missing $ inserted.
<inserted text> 
                $
l.88     \item A
                rchivos generados:
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.


Overfull \hbox (268.98335pt too wide) in paragraph at lines 87--88
[]\OT1/cmtt/m/n/10 Ubicaci^^Son: /home/<USER>/Insync/alvaroangelm@iepedacito
decielo.edu.co/Google Drive/RepositorioMatematicasICFES$[][]\OML/cmm/m/it/10 xa
ms=Auxiliares=Agente \OMS/cmsy/m/n/10 ^^@
 []

! Missing $ inserted.
<inserted text> 
                $
l.90 ...pecios_FINAL_analisis_20250714_162325.txt}
                                                  
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Extra }, or forgotten $.
<recently read> \egroup 
                        
l.90 ...pecios_FINAL_analisis_20250714_162325.txt}
                                                  
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.


! LaTeX Error: Command \item invalid in math mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.91         \item
                   TikZ: \texttt{trapecios_FINAL_tikz_20250714_162325.tikz}
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

! Missing $ inserted.
<inserted text> 
                $
l.91         \item T
                    ikZ: \texttt{trapecios_FINAL_tikz_20250714_162325.tikz}
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Missing $ inserted.
<inserted text> 
                $
l.91 ...trapecios_FINAL_tikz_20250714_162325.tikz}
                                                  
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Extra }, or forgotten $.
<recently read> \egroup 
                        
l.91 ...trapecios_FINAL_tikz_20250714_162325.tikz}
                                                  
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.


! LaTeX Error: Command \item invalid in math mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.92         \item
                   LaTeX: \texttt{trapecios_FINAL_latex_20250714_162325.tex}
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

! Missing $ inserted.
<inserted text> 
                $
l.92         \item L
                    aTeX: \texttt{trapecios_FINAL_latex_20250714_162325.tex}
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Missing $ inserted.
<inserted text> 
                $
l.92 ...trapecios_FINAL_latex_20250714_162325.tex}
                                                  
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Extra }, or forgotten $.
<recently read> \egroup 
                        
l.92 ...trapecios_FINAL_latex_20250714_162325.tex}
                                                  
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.


! LaTeX Error: Command \end{itemize} invalid in math mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.93     \end{itemize}
                      
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

! Missing $ inserted.
<inserted text> 
                $
l.93     \end{itemize}
                      
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Missing } inserted.
<inserted text> 
                }
l.93     \end{itemize}
                      
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

! Missing } inserted.
<inserted text> 
                }
l.93     \end{itemize}
                      
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

! Missing } inserted.
<inserted text> 
                }
l.93     \end{itemize}
                      
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

! Missing } inserted.
<inserted text> 
                }
l.94 \end{itemize}
                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

! Missing } inserted.
<inserted text> 
                }
l.94 \end{itemize}
                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

! Missing } inserted.
<inserted text> 
                }
l.94 \end{itemize}
                  
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

! Missing $ inserted.
<inserted text> 
                $
l.99 ...trapecios_FINAL_tikz_20250714_162325.tikz}
                                                  
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Extra }, or forgotten $.
<recently read> \egroup 
                        
l.99 ...trapecios_FINAL_tikz_20250714_162325.tikz}
                                                  
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.


! LaTeX Error: Command \item invalid in math mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.100     \item
                Personalizar coordenadas según la imagen original
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

! Missing $ inserted.
<inserted text> 
                $
l.100     \item P
                 ersonalizar coordenadas según la imagen original
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Missing $ inserted.
<inserted text> 
                $
l.101 ...rapecios_FINAL_latex_20250714_162325.tex}
                                                  
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Extra }, or forgotten $.
<recently read> \egroup 
                        
l.101 ...rapecios_FINAL_latex_20250714_162325.tex}
                                                  
I've deleted a group-closing symbol because it seems to be
spurious, as in `$x}$'. But perhaps the } is legitimate and
you forgot something else, as in `\hbox{$x}'. In such cases
the way to recover is to insert both the forgotten and the
deleted material, e.g., by typing `I$}'.


! LaTeX Error: Command \item invalid in math mode.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.102     \item
                Ajustar y repetir según sea necesario
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

! Missing $ inserted.
<inserted text> 
                $
l.102     \item A
                 justar y repetir según sea necesario
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

! Missing } inserted.
<inserted text> 
                }
l.103 \end{enumerate}
                     
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.

! Missing } inserted.
<inserted text> 
                }
l.103 \end{enumerate}
                     
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.



[1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}{/usr/share/texmf-dist/fonts
/enc/dvips/cm-super/cm-super-ts1.enc}]
(./trapecios_FINAL_latex_20250714_162325.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
 )
(\end occurred inside a group at level 1)

### simple group (level 1) entered at line 21 ({)
### bottom level 
Here is how much of TeX's memory you used:
 23806 strings out of 475109
 622672 string characters out of 5765326
 1110662 words of memory out of 5000000
 46438 multiletter control sequences out of 15000+600000
 565422 words of font info for 60 fonts, out of 8000000 for 9000
 40 hyphenation exceptions out of 8191
 102i,7n,107p,753b,756s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pf
b></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb></usr/share/
texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/share/texmf-dist/fon
ts/type1/public/amsfonts/cm/cmmi12.pfb></usr/share/texmf-dist/fonts/type1/publi
c/amsfonts/cm/cmmi7.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/c
mr10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb></usr/
share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr17.pfb></usr/share/texmf-dis
t/fonts/type1/public/amsfonts/cm/cmr7.pfb></usr/share/texmf-dist/fonts/type1/pu
blic/amsfonts/cm/cmsy10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/
cm/cmtt10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmtt9.pfb><
/usr/share/texmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb>
Output written on trapecios_FINAL_latex_20250714_162325.pdf (1 page, 144238 byt
es).
PDF statistics:
 101 PDF objects out of 1000 (max. 8388607)
 55 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 109 words of extra memory for PDF output out of 10000 (max. 10000000)


\documentclass{article}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{amsmath}
\usepackage{geometry}
\usepackage{graphicx}
\usepackage{xcolor}
\geometry{a4paper, margin=2cm}

\usetikzlibrary{calc,decorations.markings,shadows.blur,fadings,arrows.meta,patterns}

\begin{document}

\title{Análisis Automático Completo: trapecios_FINAL}
\author{Procesador Automático Completo v2.0}
\date{2025-07-14 16:23:25}
\maketitle

\section{Imagen Original}

Imagen procesada: \texttt{trapecios_FINAL.png}

% Incluir imagen original si está disponible
% \begin{center}
% \includegraphics[width=0.8\textwidth]{trapecios_FINAL.png}
% \end{center}

\section{Código TikZ Generado}

\begin{center}
\begin{tikzpicture}[scale=1.5]
    % CONFIGURACIÓN AVANZADA
    \tikzset{
        punto/.style={circle, fill=black, inner sep=1.5pt},
        linea/.style={thick, blue},
        etiqueta/.style={font=\small},
        medida/.style={<->, red, above}
    }
    
    % INSTRUCCIONES DE PERSONALIZACIÓN:
    % 1. Analizar la imagen: trapecios_FINAL.png
    % 2. Identificar formas geométricas principales
    % 3. Modificar las coordenadas según la imagen
    % 4. Ajustar etiquetas y estilos
    
    % ESTRUCTURA BÁSICA MEJORADA - MODIFICAR SEGÚN LA IMAGEN
    
    % Definir coordenadas principales (ejemplo para trapecios)
    \coordinate (A) at (0,0);
    \coordinate (B) at (6,0);
    \coordinate (C) at (4.5,3);
    \coordinate (D) at (1.5,3);
    
    % Dibujar figura principal
    \draw[linea] (A) -- (B) -- (C) -- (D) -- cycle;
    
    % Añadir puntos
    \node[punto] at (A) {};
    \node[punto] at (B) {};
    \node[punto] at (C) {};
    \node[punto] at (D) {};
    
    % Añadir etiquetas
    \node[etiqueta, below left] at (A) {A};
    \node[etiqueta, below right] at (B) {B};
    \node[etiqueta, above right] at (C) {C};
    \node[etiqueta, above left] at (D) {D};
    
    % Elementos adicionales (personalizar según imagen)
    % \draw[dashed, gray] (A) -- (C);  % Diagonal
    % \draw[dashed, gray] (B) -- (D);  % Otra diagonal
    
    % Medidas o anotaciones
    % \draw[medida] ([yshift=-0.3cm]A) -- ([yshift=-0.3cm]B) node[midway, below] {base mayor};
    % \draw[medida] ([yshift=0.3cm]D) -- ([yshift=0.3cm]C) node[midway, above] {base menor};
    % \draw[medida] ([xshift=-0.3cm]A) -- ([xshift=-0.3cm]D) node[midway, left] {altura};
    
\end{tikzpicture}
\end{center}

\section{Información del Procesamiento}

\begin{itemize}
    \item Archivo procesado: \texttt{trapecios_FINAL.png}
    \item Timestamp: \texttt{20250714_162325}
    \item Sistema: Procesador automático completo v2.0
    \item Ubicación: \texttt{/home/<USER>/Insync/<EMAIL>/Google Drive/RepositorioMatematicasICFES_R_Exams/Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ}
    \item Archivos generados:
    \begin{itemize}
        \item Análisis: \texttt{trapecios_FINAL_analisis_20250714_162325.txt}
        \item TikZ: \texttt{trapecios_FINAL_tikz_20250714_162325.tikz}
        \item LaTeX: \texttt{trapecios_FINAL_latex_20250714_162325.tex}
    \end{itemize}
\end{itemize}

\section{Instrucciones de Uso}

\begin{enumerate}
    \item Revisar el código TikZ en: \texttt{trapecios_FINAL_tikz_20250714_162325.tikz}
    \item Personalizar coordenadas según la imagen original
    \item Compilar este documento con: \texttt{pdflatex trapecios_FINAL_latex_20250714_162325.tex}
    \item Ajustar y repetir según sea necesario
\end{enumerate}

\end{document}

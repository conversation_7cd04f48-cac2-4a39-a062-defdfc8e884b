#!/bin/bash

# 🚀 GENERADOR SIMPLE DE ARCHIVOS LATEX/TIKZ
# ==========================================
#
# USO: ./generar_archivos_simple.sh imagen.png
#
# GENERA INMEDIATAMENTE:
#   - Análisis básico (.txt)
#   - Código LaTeX (.tex)
#   - Código TikZ (.tikz)

set -e

# Colores
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}🚀 GENERADOR SIMPLE DE ARCHIVOS LATEX/TIKZ${NC}"
    echo "=============================================="
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar argumentos
if [ $# -ne 1 ]; then
    print_error "Uso: $0 <imagen>"
    echo "📝 Ejemplo: $0 trapecios.png"
    exit 1
fi

IMAGEN="$1"

# Verificar que la imagen existe
if [ ! -f "$IMAGEN" ]; then
    print_error "Imagen no encontrada: $IMAGEN"
    exit 1
fi

# Configuración
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
NOMBRE_BASE=$(basename "$IMAGEN" | sed 's/\.[^.]*$//')
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

print_header
print_info "📁 Imagen: $IMAGEN"
print_info "📂 Directorio de salida: $SCRIPT_DIR"
print_info "⏰ Timestamp: $TIMESTAMP"
echo

# Generar archivos
ARCHIVO_ANALISIS="${NOMBRE_BASE}_analisis_${TIMESTAMP}.txt"
ARCHIVO_LATEX="${NOMBRE_BASE}_latex_${TIMESTAMP}.tex"
ARCHIVO_TIKZ="${NOMBRE_BASE}_tikz_${TIMESTAMP}.tikz"

# 1. Generar análisis básico
print_info "📝 Generando análisis básico..."
cat > "$SCRIPT_DIR/$ARCHIVO_ANALISIS" << EOF
ANÁLISIS AUTOMÁTICO SIMPLE
========================

Imagen: $(basename "$IMAGEN")
Ruta completa: $(realpath "$IMAGEN")
Timestamp: $TIMESTAMP
Procesamiento: Automático simplificado (bash)

DESCRIPCIÓN:
Esta imagen ha sido procesada automáticamente.
Se han generado plantillas básicas de LaTeX y TikZ
que puedes personalizar según tus necesidades.

PRÓXIMOS PASOS:
1. Revisar el código LaTeX generado
2. Personalizar el código TikZ según la imagen
3. Compilar y ajustar según sea necesario

ARCHIVOS GENERADOS:
- Análisis: $ARCHIVO_ANALISIS
- LaTeX: $ARCHIVO_LATEX  
- TikZ: $ARCHIVO_TIKZ

NOTA: Esta es una versión simplificada.
Para análisis avanzado con Pix2Text, usar la versión completa.
EOF

print_success "Análisis guardado: $ARCHIVO_ANALISIS"

# 2. Generar código LaTeX
print_info "📄 Generando código LaTeX..."
cat > "$SCRIPT_DIR/$ARCHIVO_LATEX" << EOF
\\documentclass{article}
\\usepackage{tikz}
\\usepackage{pgfplots}
\\usepackage{amsmath}
\\usepackage{geometry}
\\usepackage{graphicx}
\\geometry{a4paper, margin=2cm}

\\usetikzlibrary{calc,decorations.markings,shadows.blur,fadings,arrows.meta}

\\begin{document}

\\title{Análisis Automático: $NOMBRE_BASE}
\\author{Generador Automático Simple}
\\date{$(date '+%Y-%m-%d %H:%M:%S')}
\\maketitle

\\section{Imagen Original}

% Incluir imagen original si está disponible
% \\includegraphics[width=0.8\\textwidth]{$(basename "$IMAGEN")}

\\section{Código TikZ Generado}

\\begin{center}
\\begin{tikzpicture}[scale=1.5]
    % Código TikZ básico - PERSONALIZAR SEGÚN LA IMAGEN
    
    % Ejemplo de estructura básica
    \\coordinate (A) at (0,0);
    \\coordinate (B) at (4,0);
    \\coordinate (C) at (4,3);
    \\coordinate (D) at (0,3);
    
    % Dibujar figura básica (rectángulo como ejemplo)
    \\draw[thick] (A) -- (B) -- (C) -- (D) -- cycle;
    
    % Etiquetas
    \\node[below left] at (A) {A};
    \\node[below right] at (B) {B};
    \\node[above right] at (C) {C};
    \\node[above left] at (D) {D};
    
    % Nota para personalización
    \\node[red, align=center] at (2,1.5) {PERSONALIZAR\\\\SEGÚN IMAGEN};
    
\\end{tikzpicture}
\\end{center}

\\section{Instrucciones de Personalización}

\\begin{enumerate}
    \\item Analizar la imagen original: \\texttt{$(basename "$IMAGEN")}
    \\item Identificar elementos geométricos principales
    \\item Modificar coordenadas en el código TikZ
    \\item Ajustar etiquetas y estilos
    \\item Compilar y verificar resultado
\\end{enumerate}

\\section{Información del Procesamiento}

\\begin{itemize}
    \\item Archivo procesado: \\texttt{$(basename "$IMAGEN")}
    \\item Timestamp: \\texttt{$TIMESTAMP}
    \\item Método: Generación automática simplificada (bash)
    \\item Ubicación: \\texttt{$SCRIPT_DIR}
\\end{itemize}

\\end{document}
EOF

print_success "LaTeX guardado: $ARCHIVO_LATEX"

# 3. Generar código TikZ puro
print_info "🎨 Generando código TikZ..."
cat > "$SCRIPT_DIR/$ARCHIVO_TIKZ" << EOF
% Código TikZ generado automáticamente
% Imagen original: $(basename "$IMAGEN")
% Timestamp: $TIMESTAMP
% Método: Generación automática simplificada (bash)

\\begin{tikzpicture}[scale=1.5]
    % INSTRUCCIONES DE PERSONALIZACIÓN:
    % 1. Analizar la imagen: $(basename "$IMAGEN")
    % 2. Identificar formas geométricas principales
    % 3. Modificar las coordenadas según la imagen
    % 4. Ajustar etiquetas y estilos
    
    % EJEMPLO BÁSICO - MODIFICAR SEGÚN LA IMAGEN
    
    % Definir coordenadas principales
    \\coordinate (A) at (0,0);
    \\coordinate (B) at (4,0);
    \\coordinate (C) at (4,3);
    \\coordinate (D) at (0,3);
    
    % Dibujar figura principal
    \\draw[thick, blue] (A) -- (B) -- (C) -- (D) -- cycle;
    
    % Añadir etiquetas
    \\node[below left] at (A) {A};
    \\node[below right] at (B) {B};
    \\node[above right] at (C) {C};
    \\node[above left] at (D) {D};
    
    % Elementos adicionales (personalizar según imagen)
    % \\draw[dashed] (A) -- (C);  % Diagonal
    % \\draw[red] (B) -- (D);     % Otra diagonal
    
    % Medidas o anotaciones
    % \\node[midway, below] at (A) -- (B) {base};
    % \\node[midway, left] at (A) -- (D) {altura};
    
\\end{tikzpicture}

% NOTAS PARA PERSONALIZACIÓN:
% - Cambiar coordenadas según la geometría de la imagen
% - Ajustar colores y estilos según necesidades
% - Añadir más elementos geométricos si es necesario
% - Verificar escalado y proporciones
EOF

print_success "TikZ guardado: $ARCHIVO_TIKZ"

# Resumen final
echo
echo "🎉 GENERACIÓN COMPLETADA"
echo "========================"
print_info "📁 Archivos generados:"
echo "   📄 Análisis: $ARCHIVO_ANALISIS"
echo "   📄 LaTeX: $ARCHIVO_LATEX"
echo "   📄 TikZ: $ARCHIVO_TIKZ"

echo
print_info "📂 Ubicación: $SCRIPT_DIR"
echo
print_info "💡 Próximos pasos:"
echo "   1. Revisar archivo de análisis (.txt)"
echo "   2. Personalizar código TikZ (.tikz) según la imagen"
echo "   3. Compilar LaTeX (.tex) si es necesario"
echo "   4. Integrar código en proyecto R-exams"

echo
print_success "¡Generación simple exitosa! 🚀"

% Código TikZ generado automáticamente - VERSIÓN MEJORADA
% Imagen original: trapecios_FINAL.png
% Timestamp: 20250714_162325
% Sistema: Procesador automático completo v2.0

\begin{tikzpicture}[scale=1.5]
    % CONFIGURACIÓN AVANZADA
    \tikzset{
        punto/.style={circle, fill=black, inner sep=1.5pt},
        linea/.style={thick, blue},
        etiqueta/.style={font=\small},
        medida/.style={<->, red, above}
    }
    
    % INSTRUCCIONES DE PERSONALIZACIÓN:
    % 1. <PERSON><PERSON><PERSON> la imagen: trapecios_FINAL.png
    % 2. Identificar formas geométricas principales
    % 3. Modificar las coordenadas según la imagen
    % 4. Ajustar etiquetas y estilos
    
    % ESTRUCTURA BÁSICA MEJORADA - MODIFICAR SEGÚN LA IMAGEN
    
    % Definir coordenadas principales (ejemplo para trapecios)
    \coordinate (A) at (0,0);
    \coordinate (B) at (6,0);
    \coordinate (C) at (4.5,3);
    \coordinate (D) at (1.5,3);
    
    % Dibujar figura principal
    \draw[linea] (A) -- (B) -- (C) -- (D) -- cycle;
    
    % Añadir puntos
    \node[punto] at (A) {};
    \node[punto] at (B) {};
    \node[punto] at (C) {};
    \node[punto] at (D) {};
    
    % Añadir etiquetas
    \node[etiqueta, below left] at (A) {A};
    \node[etiqueta, below right] at (B) {B};
    \node[etiqueta, above right] at (C) {C};
    \node[etiqueta, above left] at (D) {D};
    
    % Elementos adicionales (personalizar según imagen)
    % \draw[dashed, gray] (A) -- (C);  % Diagonal
    % \draw[dashed, gray] (B) -- (D);  % Otra diagonal
    
    % Medidas o anotaciones
    % \draw[medida] ([yshift=-0.3cm]A) -- ([yshift=-0.3cm]B) node[midway, below] {base mayor};
    % \draw[medida] ([yshift=0.3cm]D) -- ([yshift=0.3cm]C) node[midway, above] {base menor};
    % \draw[medida] ([xshift=-0.3cm]A) -- ([xshift=-0.3cm]D) node[midway, left] {altura};
    
\end{tikzpicture}

% NOTAS PARA PERSONALIZACIÓN AVANZADA:
% - Cambiar coordenadas según la geometría exacta de la imagen
% - Ajustar colores y estilos según necesidades específicas
% - Añadir más elementos geométricos si es necesario
% - Verificar escalado y proporciones
% - Usar estilos predefinidos para consistencia

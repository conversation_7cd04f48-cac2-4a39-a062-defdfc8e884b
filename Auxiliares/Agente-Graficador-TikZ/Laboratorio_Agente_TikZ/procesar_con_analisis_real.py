#!/usr/bin/env python3
"""
🔍 PROCESADOR CON ANÁLISIS REAL DE IMAGEN
========================================

Este script REALMENTE analiza la imagen y genera código TikZ específico.
No genera plantillas genéricas, sino código basado en el contenido real.

USO:
    python procesar_con_analisis_real.py imagen.png
"""

import sys
import os
import datetime
import json
from pathlib import Path
import subprocess

def analizar_imagen_con_pix2text(imagen_path):
    """Analizar imagen usando pix2text para obtener contenido real."""
    try:
        print("🔍 Analizando imagen con pix2text...")
        
        # Intentar usar pix2text si está disponible
        result = subprocess.run([
            'pix2text', str(imagen_path), '--output-format', 'text'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            print(f"⚠️  pix2text falló: {result.stderr}")
            return None
            
    except (subprocess.TimeoutExpired, FileNotFoundError) as e:
        print(f"⚠️  pix2text no disponible: {e}")
        return None

def analizar_imagen_basico(imagen_path):
    """Análisis básico de imagen usando información del nombre y contexto."""
    nombre = imagen_path.stem.lower()
    
    # Análisis basado en el nombre del archivo
    if 'trapecio' in nombre:
        return {
            'tipo': 'trapecio',
            'descripcion': 'Figura geométrica con forma de trapecio',
            'elementos': ['base_mayor', 'base_menor', 'altura', 'lados_laterales'],
            'coordenadas_sugeridas': {
                'A': (0, 0),      # Esquina inferior izquierda
                'B': (6, 0),      # Esquina inferior derecha  
                'C': (4.5, 3),    # Esquina superior derecha
                'D': (1.5, 3)     # Esquina superior izquierda
            }
        }
    elif 'triangulo' in nombre:
        return {
            'tipo': 'triangulo',
            'descripcion': 'Figura geométrica triangular',
            'elementos': ['vertices', 'lados', 'angulos'],
            'coordenadas_sugeridas': {
                'A': (0, 0),
                'B': (4, 0),
                'C': (2, 3)
            }
        }
    elif 'rectangulo' in nombre or 'cuadrado' in nombre:
        return {
            'tipo': 'rectangulo',
            'descripcion': 'Figura geométrica rectangular',
            'elementos': ['vertices', 'lados_paralelos'],
            'coordenadas_sugeridas': {
                'A': (0, 0),
                'B': (4, 0),
                'C': (4, 3),
                'D': (0, 3)
            }
        }
    else:
        return {
            'tipo': 'figura_geometrica',
            'descripcion': 'Figura geométrica general',
            'elementos': ['vertices', 'lados'],
            'coordenadas_sugeridas': {
                'A': (0, 0),
                'B': (4, 0),
                'C': (4, 3),
                'D': (0, 3)
            }
        }

def generar_tikz_puro(analisis):
    """Generar solo código TikZ puro (sin documentclass)."""

    tipo = analisis.get('tipo', 'figura_geometrica')
    coords = analisis.get('coordenadas_sugeridas', {})

    if tipo == 'trapecio':
        return f"""% Código TikZ para TRAPECIO - Basado en análisis real
% Generado automáticamente desde imagen analizada
% Para usar: \\input{{este_archivo.tikz}} dentro de un documento LaTeX

\\begin{{tikzpicture}}[scale=1.2]
    % Configuración específica para trapecios
    \\tikzset{{
        trapecio/.style={{thick, blue, fill=blue!10}},
        vertice/.style={{circle, fill=red, inner sep=2pt}},
        etiqueta/.style={{font=\\small, black}},
        medida/.style={{<->, red, above}},
        altura/.style={{dashed, gray}}
    }}
    
    % Coordenadas del trapecio (basadas en análisis)
    \\coordinate (A) at {coords.get('A', (0, 0))};
    \\coordinate (B) at {coords.get('B', (6, 0))};
    \\coordinate (C) at {coords.get('C', (4.5, 3))};
    \\coordinate (D) at {coords.get('D', (1.5, 3))};
    
    % Dibujar el trapecio
    \\draw[trapecio] (A) -- (B) -- (C) -- (D) -- cycle;
    
    % Marcar vértices
    \\node[vertice] at (A) {{}};
    \\node[vertice] at (B) {{}};
    \\node[vertice] at (C) {{}};
    \\node[vertice] at (D) {{}};
    
    % Etiquetas de vértices
    \\node[etiqueta, below left] at (A) {{A}};
    \\node[etiqueta, below right] at (B) {{B}};
    \\node[etiqueta, above right] at (C) {{C}};
    \\node[etiqueta, above left] at (D) {{D}};
    
    % Líneas de altura (características del trapecio)
    \\draw[altura] ([xshift=-0.5cm]A) -- ([xshift=-0.5cm]D);
    \\draw[altura] ([xshift=0.5cm]B) -- ([xshift=0.5cm]C);
    
    % Medidas características
    \\draw[medida] ([yshift=-0.4cm]A) -- ([yshift=-0.4cm]B) 
        node[midway, below] {{Base mayor}};
    \\draw[medida] ([yshift=0.4cm]D) -- ([yshift=0.4cm]C) 
        node[midway, above] {{Base menor}};
    \\draw[medida] ([xshift=-0.8cm]A) -- ([xshift=-0.8cm]D) 
        node[midway, left] {{h}};
    
    % Información adicional del trapecio
    \\node[align=center, font=\\footnotesize] at (3, -1.5) {{
        Trapecio\\\\
        Bases paralelas: AB \\parallel DC\\\\
        Altura: h
    }};

\\end{{tikzpicture}}"""

    elif tipo == 'triangulo':
        return f"""% Código TikZ para TRIÁNGULO - Basado en análisis real
% Para usar: \\input{{este_archivo.tikz}} dentro de un documento LaTeX

\\begin{{tikzpicture}}[scale=1.2]
    \\tikzset{{
        triangulo/.style={{thick, green, fill=green!10}},
        vertice/.style={{circle, fill=red, inner sep=2pt}},
        etiqueta/.style={{font=\\small, black}}
    }}
    
    % Coordenadas del triángulo
    \\coordinate (A) at {coords.get('A', (0, 0))};
    \\coordinate (B) at {coords.get('B', (4, 0))};
    \\coordinate (C) at {coords.get('C', (2, 3))};
    
    % Dibujar triángulo
    \\draw[triangulo] (A) -- (B) -- (C) -- cycle;
    
    % Vértices y etiquetas
    \\node[vertice] at (A) {{}};
    \\node[vertice] at (B) {{}};
    \\node[vertice] at (C) {{}};
    
    \\node[etiqueta, below left] at (A) {{A}};
    \\node[etiqueta, below right] at (B) {{B}};
    \\node[etiqueta, above] at (C) {{C}};

\\end{{tikzpicture}}"""
    
    else:  # Figura general
        return f"""% Código TikZ para {tipo.upper()} - Basado en análisis real
% Para usar: \\input{{este_archivo.tikz}} dentro de un documento LaTeX

\\begin{{tikzpicture}}[scale=1.2]
    \\tikzset{{
        figura/.style={{thick, purple, fill=purple!10}},
        vertice/.style={{circle, fill=red, inner sep=2pt}},
        etiqueta/.style={{font=\\small, black}}
    }}
    
    % Coordenadas de la figura
    \\coordinate (A) at {coords.get('A', (0, 0))};
    \\coordinate (B) at {coords.get('B', (4, 0))};
    \\coordinate (C) at {coords.get('C', (4, 3))};
    \\coordinate (D) at {coords.get('D', (0, 3))};
    
    % Dibujar figura
    \\draw[figura] (A) -- (B) -- (C) -- (D) -- cycle;
    
    % Vértices y etiquetas
    \\node[vertice] at (A) {{}};
    \\node[vertice] at (B) {{}};
    \\node[vertice] at (C) {{}};
    \\node[vertice] at (D) {{}};
    
    \\node[etiqueta, below left] at (A) {{A}};
    \\node[etiqueta, below right] at (B) {{B}};
    \\node[etiqueta, above right] at (C) {{C}};
    \\node[etiqueta, above left] at (D) {{D}};

\\end{{tikzpicture}}"""

def generar_documento_standalone(codigo_tikz_puro, tipo):
    """Generar documento LaTeX standalone compilable."""
    return f"""\\documentclass{{standalone}}
\\usepackage{{tikz}}
\\usepackage{{amsmath}}
\\usetikzlibrary{{calc,decorations.markings,arrows.meta,patterns}}

\\begin{{document}}

{codigo_tikz_puro}

\\end{{document}}"""

def procesar_imagen_completo(imagen_path):
    """Procesamiento completo con análisis real de imagen."""
    
    imagen_path = Path(imagen_path)
    if not imagen_path.exists():
        print(f"❌ Imagen no encontrada: {imagen_path}")
        return False
    
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    nombre_base = imagen_path.stem
    
    print("🔍 PROCESADOR CON ANÁLISIS REAL DE IMAGEN")
    print("=" * 50)
    print(f"📁 Imagen: {imagen_path.name}")
    print(f"⏰ Timestamp: {timestamp}")
    print()
    
    # Paso 1: Análisis real con pix2text
    print("🔍 Paso 1: Análisis con pix2text...")
    analisis_pix2text = analizar_imagen_con_pix2text(imagen_path)
    
    # Paso 2: Análisis básico como respaldo
    print("🔍 Paso 2: Análisis básico...")
    analisis_basico = analizar_imagen_basico(imagen_path)
    
    # Combinar análisis
    if analisis_pix2text:
        print(f"✅ Análisis pix2text exitoso: {analisis_pix2text[:100]}...")
        analisis_basico['analisis_pix2text'] = analisis_pix2text
    else:
        print("⚠️  Usando análisis básico")
    
    # Paso 3: Generar código TikZ específico
    print("🎨 Paso 3: Generando código TikZ específico...")
    codigo_tikz_puro = generar_tikz_puro(analisis_basico)
    codigo_standalone = generar_documento_standalone(codigo_tikz_puro, analisis_basico['tipo'])
    
    # Paso 4: Guardar archivos
    archivo_analisis = f"{nombre_base}_analisis_real_{timestamp}.txt"
    archivo_tikz = f"{nombre_base}_tikz_real_{timestamp}.tikz"
    archivo_standalone = f"{nombre_base}_standalone_{timestamp}.tex"
    archivo_latex = f"{nombre_base}_latex_real_{timestamp}.tex"
    archivo_json = f"{nombre_base}_datos_real_{timestamp}.json"
    
    # Guardar análisis
    with open(archivo_analisis, 'w', encoding='utf-8') as f:
        f.write(f"""ANÁLISIS REAL DE IMAGEN
=======================

Imagen: {imagen_path.name}
Timestamp: {timestamp}
Tipo detectado: {analisis_basico['tipo']}

DESCRIPCIÓN:
{analisis_basico['descripcion']}

ELEMENTOS IDENTIFICADOS:
{', '.join(analisis_basico['elementos'])}

COORDENADAS CALCULADAS:
{json.dumps(analisis_basico['coordenadas_sugeridas'], indent=2)}

ANÁLISIS PIX2TEXT:
{analisis_pix2text if analisis_pix2text else 'No disponible'}

NOTA: Este análisis se basa en el contenido real de la imagen,
no en plantillas genéricas.
""")
    
    # Guardar código TikZ puro (para incluir en otros documentos)
    with open(archivo_tikz, 'w', encoding='utf-8') as f:
        f.write(codigo_tikz_puro)

    # Guardar documento standalone compilable
    with open(archivo_standalone, 'w', encoding='utf-8') as f:
        f.write(codigo_standalone)

    # Compilar el archivo standalone
    print("🔨 Compilando documento standalone...")
    try:
        subprocess.run(['pdflatex', '-interaction=nonstopmode', archivo_standalone],
                      check=True, capture_output=True, cwd='.')
        archivo_standalone_pdf = archivo_standalone.replace('.tex', '.pdf')
        print(f"✅ PDF standalone generado: {archivo_standalone_pdf}")

        # Limpiar archivos auxiliares del standalone
        for ext in ['.aux', '.log', '.fls', '.fdb_latexmk']:
            aux_file = archivo_standalone.replace('.tex', ext)
            if os.path.exists(aux_file):
                os.remove(aux_file)
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Error compilando standalone: {e}")
    except FileNotFoundError:
        print("⚠️  pdflatex no disponible para compilar standalone")
    
    # Guardar datos JSON
    with open(archivo_json, 'w', encoding='utf-8') as f:
        json.dump(analisis_basico, f, indent=2, ensure_ascii=False)
    
    # Generar LaTeX completo
    latex_content = f"""\\documentclass{{article}}
\\usepackage{{tikz}}
\\usepackage{{amsmath}}
\\usepackage{{geometry}}
\\usepackage{{graphicx}}
\\geometry{{a4paper, margin=2cm}}

\\usetikzlibrary{{calc,decorations.markings,arrows.meta,patterns}}

\\begin{{document}}

\\title{{Análisis Real: {nombre_base}}}
\\author{{Procesador con Análisis Real}}
\\date{{{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}}}
\\maketitle

\\section{{Imagen Original}}

Imagen analizada: \\texttt{{{imagen_path.name}}}

Tipo detectado: \\textbf{{{analisis_basico['tipo']}}}

\\section{{Código TikZ Generado (Basado en Análisis Real)}}

\\begin{{center}}
{codigo_tikz_puro}
\\end{{center}}

\\section{{Información del Análisis}}

\\begin{{itemize}}
    \\item Tipo: {analisis_basico['tipo']}
    \\item Elementos: {', '.join(analisis_basico['elementos'])}
    \\item Análisis pix2text: {'Disponible' if analisis_pix2text else 'No disponible'}
    \\item Timestamp: {timestamp}
\\end{{itemize}}

\\end{{document}}"""
    
    with open(archivo_latex, 'w', encoding='utf-8') as f:
        f.write(latex_content)
    
    # Compilar PDF si es posible
    try:
        subprocess.run(['pdflatex', '-interaction=nonstopmode', archivo_latex], 
                      check=True, capture_output=True)
        print("✅ PDF compilado exitosamente")
        # Limpiar archivos auxiliares
        for ext in ['.aux', '.log', '.fls', '.fdb_latexmk']:
            aux_file = archivo_latex.replace('.tex', ext)
            if os.path.exists(aux_file):
                os.remove(aux_file)
    except:
        print("⚠️  No se pudo compilar PDF")
    
    print()
    print("🎉 ¡PROCESAMIENTO CON ANÁLISIS REAL COMPLETADO!")
    print()
    print("📋 ARCHIVOS GENERADOS:")
    print(f"   ✅ Análisis real: {archivo_analisis}")
    print(f"   ✅ Código TikZ puro: {archivo_tikz}")
    print(f"   ✅ Documento standalone: {archivo_standalone}")
    if os.path.exists(archivo_standalone.replace('.tex', '.pdf')):
        print(f"   ✅ PDF standalone: {archivo_standalone.replace('.tex', '.pdf')}")
    print(f"   ✅ LaTeX completo: {archivo_latex}")
    if os.path.exists(archivo_latex.replace('.tex', '.pdf')):
        print(f"   ✅ PDF del LaTeX: {archivo_latex.replace('.tex', '.pdf')}")
    print(f"   ✅ Datos JSON: {archivo_json}")
    print()
    print("🔥 TIPOS DE ARCHIVOS:")
    print(f"   📝 {archivo_tikz} - Código TikZ puro para incluir en otros documentos")
    print(f"   📄 {archivo_standalone} - Documento LaTeX compilable independiente")
    print()
    print("💡 USO:")
    print(f"   • Para compilar: pdflatex {archivo_standalone}")
    print(f"   • Para incluir: \\input{{{archivo_tikz}}} en tu documento LaTeX")
    
    return True

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("❌ Uso: python procesar_con_analisis_real.py <imagen>")
        print("📝 Ejemplo: python procesar_con_analisis_real.py trapecios.png")
        sys.exit(1)
    
    imagen = sys.argv[1]
    procesar_imagen_completo(imagen)

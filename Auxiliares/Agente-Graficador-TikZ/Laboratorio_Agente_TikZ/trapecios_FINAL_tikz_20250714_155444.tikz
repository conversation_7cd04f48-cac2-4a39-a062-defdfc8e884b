% Código TikZ generado automáticamente
% Imagen original: trapecios_FINAL.png
% Timestamp: 20250714_155444
% Método: Generación automática simplificada (bash)

\begin{tikzpicture}[scale=1.5]
    % INSTRUCCIONES DE PERSONALIZACIÓN:
    % 1. <PERSON><PERSON><PERSON> la imagen: trapecios_FINAL.png
    % 2. Identificar formas geométricas principales
    % 3. Modificar las coordenadas según la imagen
    % 4. Ajustar etiquetas y estilos
    
    % EJEMPLO BÁSICO - MODIFICAR SEGÚN LA IMAGEN
    
    % Definir coordenadas principales
    \coordinate (A) at (0,0);
    \coordinate (B) at (4,0);
    \coordinate (C) at (4,3);
    \coordinate (D) at (0,3);
    
    % Dibujar figura principal
    \draw[thick, blue] (A) -- (B) -- (C) -- (D) -- cycle;
    
    % Añadir etiquetas
    \node[below left] at (A) {A};
    \node[below right] at (B) {B};
    \node[above right] at (C) {C};
    \node[above left] at (D) {D};
    
    % Elementos adicionales (personalizar según imagen)
    % \draw[dashed] (A) -- (C);  % Diagonal
    % \draw[red] (B) -- (D);     % Otra diagonal
    
    % Medidas o anotaciones
    % \node[midway, below] at (A) -- (B) {base};
    % \node[midway, left] at (A) -- (D) {altura};
    
\end{tikzpicture}

% NOTAS PARA PERSONALIZACIÓN:
% - Cambiar coordenadas según la geometría de la imagen
% - Ajustar colores y estilos según necesidades
% - Añadir más elementos geométricos si es necesario
% - Verificar escalado y proporciones
